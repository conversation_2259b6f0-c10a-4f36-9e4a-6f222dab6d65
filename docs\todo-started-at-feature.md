# Tính Năng StartedAt trong Todo Module

## 📋 Tổng Quan

Tính năng `startedAt` được thêm vào Todo module để theo dõi thời gian bắt đầu làm việc thực tế của từng công việc. Điều này giúp:

- <PERSON> dõi thời gian làm việc thực tế
- Phân tích hiệu suất và tiến độ
- T<PERSON>o báo cáo thời gian làm việc
- Tối ưu hóa quy trình làm việc

## 🔧 Thay Đổi <PERSON>ỹ Thuật

### 1. Database Schema

**Cột mới được thêm vào bảng `todos`:**
```sql
ALTER TABLE todos ADD COLUMN started_at BIGINT NULL;
CREATE INDEX idx_todos_started_at ON todos(started_at);
```

### 2. Entity Updates

**Todo Entity (`src/modules/todolists/entities/todo.entity.ts`):**
```typescript
@Column({ name: 'started_at', type: 'bigint', nullable: true })
startedAt: number | null;
```

### 3. DTO Updates

**TodoResponseDto:**
```typescript
@ApiProperty({
  description: 'Thời gian bắt đầu làm việc (timestamp)',
  example: 1625097600000,
  nullable: true,
})
startedAt: number | null;
```

**UpdateTodoDto:**
```typescript
@ApiProperty({
  description: 'Thời gian bắt đầu làm việc (timestamp trong milliseconds)',
  example: 1625097600000,
  required: false,
})
@IsOptional()
@IsInt({ message: 'Thời gian bắt đầu phải là số nguyên (timestamp)' })
startedAt?: number;
```

**CreateTodoDto:**
```typescript
@ApiProperty({
  description: 'Thời gian bắt đầu làm việc (timestamp trong milliseconds)',
  example: 1625097600000,
  required: false,
})
@IsOptional()
@IsInt({ message: 'Thời gian bắt đầu phải là số nguyên (timestamp)' })
startedAt?: number;
```

## 🚀 Cách Sử Dụng

### 1. Tạo Todo với StartedAt

```typescript
// API: POST /api/todos
{
  "title": "Thiết kế giao diện",
  "description": "Thiết kế giao diện cho trang chủ",
  "assigneeId": 1,
  "priority": "high",
  "startedAt": 1625097600000  // Tùy chọn
}
```

### 2. Cập Nhật StartedAt

```typescript
// API: PUT /api/todos/:id
{
  "startedAt": 1625097600000
}
```

### 3. Tự Động Set StartedAt

Khi cập nhật trạng thái todo từ `PENDING` sang `IN_PROGRESS`, hệ thống sẽ tự động set `startedAt` nếu chưa có:

```typescript
// API: PUT /api/todos/:id/status
{
  "status": "in_progress"
}
// startedAt sẽ được tự động set = Date.now()
```

## 📊 Logic Nghiệp Vụ

### 1. Tự Động Set StartedAt

```typescript
// Trong updateTodoStatus service
if (
  updateTodoStatusDto.status === TodoStatus.IN_PROGRESS &&
  !todo.startedAt
) {
  updateData.startedAt = Date.now();
}
```

### 2. Validation Rules

- `startedAt` là timestamp (milliseconds)
- Có thể null (chưa bắt đầu)
- Không bắt buộc khi tạo todo
- Tự động set khi chuyển sang IN_PROGRESS

### 3. Response Format

```json
{
  "id": 1,
  "title": "Thiết kế giao diện",
  "status": "in_progress",
  "createdAt": 1625097600000,
  "updatedAt": 1625097700000,
  "startedAt": 1625097650000,
  "completedAt": null,
  "deadline": 1625184000000
}
```

## 🔄 Migration

### 1. Chạy Migration

```bash
# Chạy migration để thêm cột started_at
node scripts/run-started-at-migration.js
```

### 2. Kiểm Tra Migration

```bash
# Test tính năng sau migration
node scripts/test-started-at-feature.js
```

### 3. Migration File

Migration được lưu tại: `database/migrations/004-add-started-at-to-todos.sql`

## 📈 Ứng Dụng Thực Tế

### 1. Báo Cáo Thời Gian

```sql
-- Thời gian làm việc trung bình
SELECT 
  AVG(completed_at - started_at) as avg_work_time
FROM todos 
WHERE started_at IS NOT NULL 
AND completed_at IS NOT NULL;
```

### 2. Phân Tích Hiệu Suất

```sql
-- Todos đã bắt đầu nhưng chưa hoàn thành
SELECT COUNT(*) 
FROM todos 
WHERE started_at IS NOT NULL 
AND completed_at IS NULL;
```

### 3. Tracking Progress

```sql
-- Todos theo trạng thái và thời gian bắt đầu
SELECT 
  status,
  COUNT(*) as total,
  COUNT(started_at) as started_count
FROM todos 
GROUP BY status;
```

## 🧪 Testing

### 1. Unit Tests

Cần thêm test cases cho:
- Tạo todo với startedAt
- Cập nhật startedAt
- Tự động set startedAt khi chuyển trạng thái
- Validation startedAt

### 2. Integration Tests

Test API endpoints:
- POST /api/todos (với startedAt)
- PUT /api/todos/:id (cập nhật startedAt)
- PUT /api/todos/:id/status (tự động set startedAt)

### 3. Database Tests

Test migration và performance:
- Migration script
- Index performance
- Query optimization

## 🔍 Troubleshooting

### 1. Migration Issues

```bash
# Kiểm tra cột đã được tạo
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'todos' AND column_name = 'started_at';

# Kiểm tra index
SELECT indexname FROM pg_indexes 
WHERE tablename = 'todos' AND indexname = 'idx_todos_started_at';
```

### 2. Data Consistency

```sql
-- Kiểm tra todos có startedAt nhưng status không phải IN_PROGRESS
SELECT id, title, status, started_at 
FROM todos 
WHERE started_at IS NOT NULL 
AND status = 'pending';
```

### 3. Performance Issues

```sql
-- Kiểm tra query performance
EXPLAIN ANALYZE 
SELECT * FROM todos 
WHERE started_at > 1625097600000;
```

## 📝 Notes

- StartedAt sử dụng timestamp milliseconds (giống các trường thời gian khác)
- Index được tạo để tối ưu query performance
- Tương thích với existing todo data (nullable)
- Tự động migration cho todos IN_PROGRESS hiện tại
